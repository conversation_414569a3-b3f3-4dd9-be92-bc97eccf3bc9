AC_DEFUN	aclocal.m4	/^AC_DEFUN([AM_INIT_AUTOMAKE],$/
AC_PREREQ	configure.ac	/^AC_PREREQ([2.68])$/
Copyright	COPYING	/^ Copyright (C) 1989, 1991 Free Software Foundation/
FOO	configure	/^#define FOO(x) '\\''x'\\''$/
IS	README.md	/^**I REPEAT: jool.mx IS (AND WILL PROBABLY FOREVER /
IS_DIR_SEPARATOR	ltmain.sh	/^# define IS_DIR_SEPARATOR(ch) ((ch) == DIR_SEPARAT/
IS_PATH_SEPARATOR	ltmain.sh	/^# define IS_PATH_SEPARATOR(ch) ((ch) == PATH_SEPAR/
Mconfig.guess	config.guess	/^	int main (int argc, char *argv[]) {$/
Mconfigure	configure	/^main (void)$/
STREQ	ltmain.sh	/^#define STREQ(s1, s2) (strcmp ((s1), (s2)) == 0)$/
XFREE	ltmain.sh	/^#define XFREE(stale) do { \\$/
XMALLOC	ltmain.sh	/^#define XMALLOC(type, num)      ((type *) xmalloc /
_Alignas	configure	/^char _Alignas (double) aligned_as_double;$/
ac_fn_c_check_header_compile	configure	/^ac_fn_c_check_header_compile ()$/
ac_fn_c_check_type	configure	/^ac_fn_c_check_type ()$/
ac_fn_c_try_link	configure	/^ac_fn_c_try_link ()$/
aix_post_process_depfile	depcomp	/^aix_post_process_depfile ()$/
as_fn_arith	configure	/^  eval 'as_fn_arith ()$/
as_fn_error	configure	/^as_fn_error ()$/
as_fn_executable_p	configure	/^as_fn_executable_p ()$/
as_fn_nop	configure	/^as_fn_nop ()$/
as_nl	configure	/^    case $as_nl`(ac_space=' '; set) 2>&1` in #($/
buf	configure	/^struct buf { int x; };$/
ccp	configure	/^typedef const char *ccp;$/
debug	configure	/^#define debug(...) dprintf (2, __VA_ARGS__)$/
does_not_return	configure	/^int _Noreturn does_not_return (void) { for (;;) co/
forced_loaded	configure	/^int forced_loaded() { return 2;}$/
func_append_quoted	ltmain.sh	/^  eval 'func_append_quoted ()$/
func_arith	ltmain.sh	/^  eval 'func_arith ()$/
func_cc_basename	configure	/^func_cc_basename ()$/
func_file_conv	compile	/^func_file_conv ()$/
func_unset	ltmain.sh	/^func_unset ()$/
if	configure	/^if (exec 3>&0) 2>\/dev\/null; then :; else exec 0</
long_ptr	configure	/^typedef long *long_ptr;$/
m4_ifndef	aclocal.m4	/^m4_ifndef([AC_CONFIG_MACRO_DIRS], [m4_defun([_AM_C/
make_dummy_depfile	depcomp	/^make_dummy_depfile ()$/
named_init	configure	/^struct named_init {$/
report	configure	/^#define report(test,...) ((test) ? puts (#test) : /
s1	configure	/^struct s1 {int (*f) (int a);};$/
s2	configure	/^struct s2 {int (*f) (double a);};$/
set_dir_from	depcomp	/^set_dir_from ()$/
showlist	configure	/^#define showlist(...) puts (#__VA_ARGS__)$/
test_restrict	configure	/^test_restrict (ccp restrict text)$/
test_varargs	configure	/^test_varargs (const char *format, ...)$/
