diff a/kernel_modules/jool/src/mod/common/skbuff.c b/kernel_modules/jool/src/mod/common/skbuff.c	(rejected hunks)
@@ -109,9 +109,9 @@ static void print_skb_fields(struct sk_buff *skb, unsigned int tabs)
 	print(tabs, "network_header:%u", skb->network_header);
 	print(tabs, "mac_header:%u", skb->mac_header);
 	print(tabs, "head:%p", skb->head);
-	print(tabs, "data:%ld", skb->data - skb->head);
-	print(tabs, "tail:%u", skb->tail);
-	print(tabs, "end:%u", skb->end);
+	print(tabs, "data:%ld", (long)(skb->data - skb->head));
+	print(tabs, "tail:%u", (unsigned int)skb->tail);
+	print(tabs, "end:%u", (unsigned int)skb->end);
 }
 
 static int truncated(unsigned int tabs)
