diff a/kernel_modules/jool/src/mod/common/nl/instance.c b/kernel_modules/jool/src/mod/common/nl/instance.c	(rejected hunks)
@@ -37,7 +37,7 @@ static int serialize_instance(struct xlator *entry, void *arg)
 	if (!root)
 		return 1;
 
-	error = nla_put_u32(skb, JNLAIE_NS, ((__u64)entry->ns) & 0xFFFFFFFF);
+	error = nla_put_u32(skb, JNLAIE_NS, ((uintptr_t)entry->ns) & 0xFFFFFFFF);
 	if (error)
 		goto cancel;
 	error = nla_put_u8(skb, JNLAIE_XF, xlator_flags2xf(entry->flags));
