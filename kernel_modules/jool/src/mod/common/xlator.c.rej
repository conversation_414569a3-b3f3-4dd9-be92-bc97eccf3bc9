diff a/kernel_modules/jool/src/mod/common/xlator.c b/kernel_modules/jool/src/mod/common/xlator.c	(rejected hunks)
@@ -875,7 +875,7 @@ void xlator_put(struct xlator *jool)
 static bool offset_equals(struct instance_entry_usr *offset,
 		struct jool_instance *instance)
 {
-	return (offset->ns == ((__u64)instance->jool.ns & 0xFFFFFFFF))
+	return (offset->ns == ((uintptr_t)instance->jool.ns & 0xFFFFFFFF))
 			&& (strcmp(offset->iname, instance->jool.iname) == 0);
 }
 
